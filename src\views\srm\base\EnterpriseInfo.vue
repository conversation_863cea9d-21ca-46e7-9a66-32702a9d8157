<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url" />
    <field-select-modal ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      v-if="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { USER_INFO, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { SelectModal } from '@comp/template/business/class/selectModal'

import REGEXP from '@/utils/regexp'
import { srmI18n, getLangAccount } from '@/utils/util.js'
const HOMEPAGE = process.env.VUE_APP_CONFIG_COMPANY_HOME_PAGE
export default {
    name: 'ElsEnterpriseInfoEdit',
    inject: ['closeCurrent'],
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        const _self = this
        return {
            fileSrc: '',
            pageData: {
                form: {},
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactInfo`, '联系人信息'),
                        groupCode: 'contactsInfo',
                        type: 'grid',
                        needDynamicsHeight: true,
                        custom: {
                            ref: 'supplierContactsInfoList',
                            columns: [
                                {
                                    type: 'checkbox',
                                    width: 40
                                },
                                {
                                    type: 'seq',
                                    width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                    field: 'elsAccount',
                                    width: 150
                                },
                                {
                                    title: '采购ELS账号',
                                    field: 'toElsAccount',
                                    width: 150
                                },
                                {
                                    ...new SelectModal({
                                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                                        field: 'subAccount',
                                        required: '1',
                                        width: 150,
                                        bindFunction: function (row, data) {
                                            (row.subAccount = data[0].subAccount), (row.name = data[0].realname), (row.telphone = data[0].phone), (row.email = data[0].email)
                                        },
                                        extend: {
                                            modalColumns: [
                                                {
                                                    field: 'subAccount',
                                                    title: srmI18n(`${getLangAccount()}#i18n_field_subAccount`, '子账号'),
                                                    with: 150
                                                },
                                                {
                                                    field: 'realname',
                                                    title: srmI18n(`${getLangAccount()}#i18n_title_name`, '姓名'),
                                                    with: 150
                                                },
                                                {
                                                    field: 'phone',
                                                    title: srmI18n(`${getLangAccount()}#i18n_title_telphone`, '电话号码'),
                                                    with: 150
                                                },
                                                {
                                                    field: 'email',
                                                    title: srmI18n(`${getLangAccount()}#i18n_field_email`, '邮箱'),
                                                    with: 150
                                                }
                                            ],
                                            modalUrl: '/account/elsSubAccount/list',
                                            modalParams: {},
                                            afterRowClearCallBack: function (row){
                                                row.subAccount = ''
                                                row.name = ''
                                                row.telphone = ''
                                                row.email = ''
                                            }
                                        }
                                    })
                                },
                                {
                                    required: '0',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_function`, '职能'),
                                    field: 'functionName',
                                    width: 150,
                                    dictCode: 'JobFunction',
                                    editRender: { name: '$select', props: {clearable: true} }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_position`, '职位'),
                                    required: '1',
                                    field: 'position',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                    field: 'name',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话号码'),
                                    field: 'telphone',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_email`, '邮箱'),
                                    required: '1',
                                    field: 'email',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: 'QQ',
                                    field: 'qqNo',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_weChat`, '微信'),
                                    field: 'wxNo',
                                    width: 150,
                                    editRender: { name: '$input' }
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.addContactsItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteContactsEvent
                                }
                            ],
                            rules: {
                                subAccount: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSubaccountCannotBeEmpty`, '子账号不能为空')
                                    }
                                ],
                                functionName: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_functionCannotBeEmpty`, '职能不能为空')
                                    }
                                ],
                                position: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_positionBeEmpty`, '职位不能为空')
                                    }
                                ],
                                name: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameCannotBeEmpty`, '姓名不能为空')
                                    }
                                ],
                                telphone: [
                                    {
                                        validator: function ({ ...obj }) {
                                            if (obj.cellValue === '' || obj.cellValue === null || typeof obj.cellValue === undefined) {
                                                return false
                                            } else {
                                                if (REGEXP.mobile.test(obj.cellValue)) {
                                                    return false
                                                } else {
                                                    return new Error(_self.$srmI18n(`${_self.$getLangAccount()}#i18n_title_phoneError`, '手机号不正确'))
                                                }
                                            }
                                        }
                                    }
                                ],
                                email: [
                                    //     {
                                    //     pattern: REGEXP.email,
                                    //     message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_emailError`, '邮箱格式不正确')
                                    // }

                                    {
                                        validator: function ({ ...obj }) {
                                            if (obj.cellValue === '' || obj.cellValue === null || typeof obj.cellValue === undefined) {
                                                return false
                                            } else {
                                                if (REGEXP.email.test(obj.cellValue)) {
                                                    return false
                                                } else {
                                                    return new Error(_self.$srmI18n(`${_self.$getLangAccount()}#i18n_title_emailError`, '邮箱格式不正确'))
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressInfo`, '地址信息'),
                        groupCode: 'addressInfo',
                        type: 'grid',
                        custom: {
                            ref: 'supplierAddressInfoList',
                            columns: [
                                {
                                    type: 'checkbox',
                                    width: 40
                                },
                                {
                                    type: 'seq',
                                    width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                    field: 'elsAccount',
                                    width: 150
                                },
                                {
                                    title: '采购ELS账号',
                                    field: 'toElsAccount',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_country`, '国家'),
                                    field: 'country',
                                    width: 150,
                                    required: '1',
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_province`, '省份'),
                                    field: 'province',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_city`, '城市'),
                                    field: 'city',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_address`, '详细地址'),
                                    field: 'address',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_telphone`, '电话'),
                                    field: 'telphone',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zipCode`, '邮编'),
                                    field: 'fax',
                                    width: 150,
                                    editRender: { name: '$input', props: { type: 'number' } }
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.addAddressItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteAddressEvent
                                }
                            ],
                            rules: {
                                country: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_countryCannotBeEmpty`, '国家不能为空')
                                    }
                                ],
                                province: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_provinceCannotBeEmpty`, '省份不能为空')
                                    }
                                ],
                                city: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cityCannotBeEmpty`, '城市不能为空')
                                    }
                                ],
                                address: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addressCannotBeEmpty`, '地址不能为空')
                                    }
                                ],
                                telphone: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phoneCannotBeEmpty`, '电话不能为空')
                                    }
                                ],
                                fax: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zipcodeCannotBeEmpty`, '邮编不能为空')
                                    }
                                ]
                            }
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankInfo`, '银行信息'),
                        groupCode: 'bankInfo',
                        type: 'grid',
                        custom: {
                            ref: 'supplierBankInfoList',
                            columns: [
                                {
                                    type: 'checkbox',
                                    width: 40
                                },
                                {
                                    type: 'seq',
                                    width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ELSAccount`, 'ELS账号'),
                                    field: 'elsAccount',
                                    width: 150
                                },
                                {
                                    title: '采购ELS账号',
                                    field: 'toElsAccount',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCountry`, '银行国家'),
                                    field: 'bankCountry',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankProvince`, '银行省份'),
                                    field: 'bankProvince',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCity`, '银行城市'),
                                    field: 'bankCity',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankBranchName`, '开户行全称'),
                                    field: 'bankBranchName',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccount`, '银行账号'),
                                    field: 'bankAccount',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccountName`, '银行账号名称'),
                                    field: 'bankAccountName',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cooperationBankType`, '合作银行类型'),
                                    field: 'cooperationBankType',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCode`, '银行代码'),
                                    field: 'bankCode',
                                    required: '1',
                                    width: 150,
                                    editRender: { name: '$input' }
                                }
                                ,
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CLeD_4a527e9d`, '默认账户'),
                                    field: 'defaultAccount',
                                    required: '1',
                                    defaultValue: '1',
                                    width: 150,
                                    editRender: { 
                                        name: '$select',
                                        options: [
                                            {
                                                label: '是',
                                                value: '1'
                                            },
                                            {
                                                label: '否',
                                                value: '0'
                                            }
                                        ]
                                    }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCodeIBAN`, 'IBAN（国际银行帐户号码）'),
                                    field: 'iban',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_swiftCode`, 'SWIFT CODE（银行国际代码）'),
                                    field: 'swiftCode',
                                    width: 150,
                                    editRender: { name: '$input' }
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedName`, '开户资料扫描件名称'),
                                    field: 'fileName',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openAccountScannedPath`, '开户资料扫描件地址'),
                                    field: 'filePath',
                                    width: 150
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: { default: 'grid_opration' }
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.addBankItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteBankEvent
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload',
                                    businessType: 'supplierMasterData',
                                    beforeChecked: true,
                                    beforeCheckedCallBack: this.bankFileUploadCallBack,
                                    single: true,
                                    modalVisible: false,
                                    callBack: this.uploadCallBack
                                }
                            ],
                            rules: {
                                bankCountry: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCountryCannotBeEmpty`, '银行国家不能为空')
                                    }
                                ],
                                bankProvince: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankProvinceCannotBeEmpty`, '银行省份不能为空')
                                    }
                                ],
                                bankCity: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCityCannotBeEmpty`, '银行城市不能为空')
                                    }
                                ],
                                bankBranchName: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankBranchNameCannotBeEmpty`, '开户行全称不能为空')
                                    }
                                ],
                                bankAccount: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankAccountCannotBeEmpty`, '银行账号不能为空')
                                    }
                                ],
                                bankAccountName: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankNameCannotBeEmpty`, '银行名称不能为空')
                                    }
                                ],
                                bankCode: [
                                    {
                                        required: true,
                                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankCodeCannotBeEmpty`, '银行代码不能为空')
                                    }
                                ],
                                // defaultAccount: [
                                //     {
                                //         required: true,
                                //         message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CLeDxOLV_eef0ec2d`, '默认账户不能为空')
                                //     }
                                // ]
                            },
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.bankFileDownloadEvent
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'supplierInfoChangeAttachmentList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: { default: 'grid_opration' }
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload',
                                    businessType: 'supplierMasterData',
                                    callBack: this.uploadInfoChangCallBack
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                                    click: this.deleteBatch
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                {
                                    type: 'delete',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    clickFn: this.deleteFilesEvent
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                extraBtn: [{ type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_AEdE_25190693`, '企业主页'), click: this.toTanentPageHome }],
                footerButtons: [
                    {
                        showCondition: this.showCondition,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        type: 'primary',
                        click: this.saveEvent,
                        showCondition: this.isShowSave
                    },
                    {
                        showCondition: this.isShowRelease,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_savePublish`, '保存并发布'),
                        type: 'primary',
                        click: this.releaseEvent
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#`, '关闭'),
                        click: this.goBack
                    }
                ]
            },
            url: {
                edit: '/enterprise/elsEnterpriseInfo/editEnterpriseInfo',
                detail: '/supplier/supplierMaster/queryCurrentEnterprise',
                release: '/supplier/supplierInfoChangeHead/releaseByIds',
                upload: '/attachment/purchaseAttachment/upload',
                download: '/attachment/purchaseAttachment/download'
            }
        }
    },
    created () {
        this.getFileSrc()
    },
    methods: {
        // 跳转到企业主页
        toTanentPageHome () {
            let elsAccount = this.$ls.get(USER_ELS_ACCOUNT)
            window.open(`${HOMEPAGE}?elsAccount=${elsAccount}`, '_blank')
        },
        goBack () {
            this.closeCurrent()
        },
        isShowSave () {
            return this.$hasOptAuth('enterpriseInfo#enterpriseInfo:save')
        },
        isShowRelease () {
            return this.$hasOptAuth('enterpriseInfo#enterpriseInfo:release')
        },
        init () {
            this.$refs.editPage.queryDetailNotid()
        },
        bankFileDownloadEvent (row) {
            this.downloadFile(row)
        },
        certificateFileDownloadEvent (row) {
            this.downloadFile(row)
        },
        downloadFile (row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }

            let fileNameStr = row.fileName
            if (row.fileName.includes('➔')) {
                if (fileNameStr.split('➔').length > 1 && (fileNameStr.split('➔')[1]!=='' && fileNameStr.split('➔')[1]!==null)) {
                    fileNameStr = fileNameStr.split('➔')[1].trim()
                } else {
                    fileNameStr = fileNameStr.split('➔')[0].trim()
                }
            }

            const [id, fileName] = fileNameStr.split('-')
            const params = {
                id
            }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        bankFileUploadCallBack (data) {
            return new Promise((resolve) => {
                // 验证的规则
                if (data && data.length) {
                    if (data.length === 1) {
                        resolve(true)
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onlySelectOne`, '只能选择一条'))
                        resolve(false)
                    }
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataoPerated`, '请选择需要操作的数据'))
                    resolve(false)
                }
            })
        },
        certificateFileUploadCallBack (data) {
            return new Promise((resolve) => {
                // 验证的规则
                if (data && data.length) {
                    if (data.length === 1) {
                        resolve(true)
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onlySelectOne`, '只能选择一条'))
                        resolve(false)
                    }
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectDataoPerated`, '请选择需要操作的数据'))
                    resolve(false)
                }
            })
        },
        uploadCallBack (result, refName) {
            console.log(result)
            const { id = '', fileName = '', filePath = '' } = result[0] || {}
            const fileGrid = this.$refs.editPage.$refs[refName][0].getCheckboxRecords()
            fileGrid[0].filePath = `${filePath}`
            fileGrid[0].fileName = `${id}-${fileName}`
        },
        addContactsItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierContactsInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach((item) => {
                if (item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form = this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.elsAccount
            itemData['toElsAccount'] = '********'
            itemGrid.insert([itemData])
        },
        deleteContactsEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierContactsInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        addAddressItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierAddressInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach((item) => {
                if (item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form = this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.elsAccount
            itemData['toElsAccount'] = '********'
            itemGrid.insert([itemData])
        },
        deleteAddressEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierAddressInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        addBankItem () {
            let itemGrid = this.$refs.editPage.$refs.supplierBankInfoList[0]
            let itemData = {}
            this.pageConfig.itemColumns.forEach((item) => {
                if (item.defaultValue) {
                    itemData[item.field] = item.defaultValue
                }
            })
            const form = this.$refs.editPage.getPageData()
            itemData['elsAccount'] = form.elsAccount
            itemData['toElsAccount'] = '********'
            // itemData.defaultAccount = '1'
            itemGrid.insert([itemData])
        },
        deleteBankEvent () {
            let itemGrid = this.$refs.editPage.$refs.supplierBankInfoList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        getFileSrc () {
            let time = new Date().getTime()
            const param = { businessType: 'enterprise', elsAccount: '********' }
            console.log(param)
            getAction('/template/templateHead/noToken/listByBusiness', param).then((res) => {
                const type = res.success ? 'success' : 'error'
                console.log(res)
                if (type === 'success' && res.result && res.result.records) {
                    let templateNumber = res.result.records[0].templateNumber
                    let templateVersion = res.result.records[0].templateVersion
                    this.fileSrc = `${this.$variateConfig['configFiles']}/********/purchase_enterprise_${templateNumber}_${templateVersion}.js?t=` + time
                }
            })
        },
        fieldSelectOk (data) {
            let supplierGrid = this.$refs.editPage.$refs.supplierOrgInfoList[0]
            let { fullData } = supplierGrid.getTableData()
            let supplierList = fullData.map((item) => {
                return item.elsAccount
            })
            // 过滤已有数据
            let insertData = data.filter((item) => {
                return !supplierList.includes(item.elsAccount)
            })
            insertData = insertData.map((item) => {
                return {
                    toElsAccount: item.elsAccount
                }
            })
            supplierGrid.insertAt(insertData, -1)
        },
        // 保存
        saveEvent () {
            this.editHandleData('0')
        },
        // 发布
        releaseEvent () {
            this.editHandleData('1')
        },
        // 编辑请求接口处理
        editHandleData (updateType) {
            const params = this.$refs.editPage.getPageData()
            params['updateType'] = updateType
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => ({
                            status: 'success',
                            res
                        }),
                        (err) => ({
                            status: 'error',
                            err
                        })
                    )
                )
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise))
                .then((result) => {
                    // 校验规则
                    const errorIndex = result.findIndex((n) => n.status === 'error')
                    if (errorIndex !== -1) {
                        this.$refs.editPage.currentStep = errorIndex
                        return
                    }

                    // let supplierBankInfoListElement = params.supplierBankInfoList ? params.supplierBankInfoList:[]
                    // let arr = supplierBankInfoListElement.filter(item =>  item.defaultAccount=='1')
                    // if(arr.length>1) {
                    //     this.$message.error('供应商银行信息默认账号只能有一个')
                    //     return
                    // }
                    // if(arr.length==0) {
                    //     this.$message.error('供应商银行信息默认账号至少有一个')
                    //     return
                    // }

                    this.updateLogoSrc(params)
                    let url = this.url.edit
                    this.$refs.editPage.confirmLoading = true
                    postAction(url, params).then((res) => {
                        if (res.success) {
                            this.$message.success(res.result.msgInfo)
                            this.init()
                            this.$refs.editPage.form = res.result
                        } else {
                            this.$message.warning(res.message)
                        }
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                })
                .catch((err) => {
                    console.log(err)
                })
        },
        updateLogoSrc (params) {
            let oldUserInfo = this.$ls.get(USER_INFO)
            oldUserInfo.enterpriseLogo = params.enterpriseLogo || ''
            // 更新logosrc
            this.$ls.set(USER_INFO, oldUserInfo, 7 * 24 * 60 * 60 * 1000)
        },
        publishEvent () {
            this.$refs.editPage.handleSend()
        },
        // 上传企业信息变更文件回调事件
        uploadInfoChangCallBack (result, refName) {
            console.log(result)
            let fileGrid = this.$refs.editPage.$refs.supplierInfoChangeAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        // 下载企业信息变更文件
        downloadEvent (row) {
            const params = { id: row.id }
            getAction('/attachment/purchaseAttachment/download', params, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 预览企业信息变更文件
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        // 删除企业信息变更文件
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.supplierInfoChangeAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', { id: row.id }).then((res) => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.supplierInfoChangeAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map((n) => n.id).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then((res) => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        }
    }
}
</script>
