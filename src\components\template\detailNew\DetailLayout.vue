<template>
  <div class="detail-page">
    <a-spin :spinning="confirmLoading">
      <div class="container">
        <div
          class="top"
          v-if="showHeader"
        >
          <div class="breadcrumb">
            <slot name="title-page">
              <span>{{ currentPageName }}</span>
              <!-- <breadcrumb></breadcrumb> -->
            </slot>
          </div>
          <div class="btnGroups">
            <template v-if="taskInfo.taskId && APPROVAL_PATH_LIST.includes($route.path)">
              <taskBtn
                :currentEditRow="currentEditRow"
                :resultData="form"
                :pageHeaderButtons="getAuthCodeBtns(pageData.publicBtn)"
              />
            </template>
            <template v-else>
              <!-- <a-button
                v-for="(btn,index) in getAuthCodeBtns(pageData.publicBtn)"
                :key="'pub_btn_' + index"
                :type="btn.type"
                v-show="btn.showCondition ? btn.showCondition() : true"
                :disabled="btn.disabled ? btn.disabled() : false"
                @click="btn.click"
              >
                {{ btn.title }}
              </a-button> -->
              <a-tooltip
                placement="bottom"
                v-for="(btn, index) in getAuthCodeBtns(pageData.publicBtn)"
                :key="'pub_btn_' + index"
              >
                <template
                  slot="title"
                  v-if="btn.helpText"
                >
                  <span v-html="btn.helpText"></span>
                </template>
                <a-button
                  :type="btn.type"
                  v-show="btn.showCondition ? btn.showCondition() : true"
                  :disabled="btn.disabled ? btn.disabled() : false"
                  @click="btn.click"
                  :icon="btn.icon"
                  >{{ btn.title }}</a-button
                >
              </a-tooltip>
            </template>
          </div>
        </div>
        <!-- <div
          class="sub-top"
          v-if="elsStatusLog && elsStatusLog.list && elsStatusLog.list.length">
          <div class="step-status">
            <a-steps :current="elsStatusLog.currentStatusIndex">
              <a-popover
                slot="progressDot"
                slot-scope="{ status, prefixCls }">
                <template slot="content">
                  <span v-if="status==='wait'">{{ $srmI18n(`${busAccount}#i18n_field_EoGv_397ca1de`, '等待处理') }}</span>
                  <span v-if="status==='process'">{{ $srmI18n(`${busAccount}#i18n_field_Gvs_15c82eb`, '处理中') }}</span>
                  <span v-if="status==='finish'">{{ $srmI18n(`${busAccount}#i18n_field_IML_16c2176`, '已完成') }}</span>
                </template>
                <span :class="`${prefixCls}-icon-dot`" />
              </a-popover>
              <a-step
                v-for="(value, index ) in elsStatusLog.list"
                :key="index"
                :title="value.businessStatus_dictText"
                :description="value.statusDate"/>
            </a-steps>
          </div>
        </div> -->
        <div class="content">
          <!-- tab -->
          <template v-if="displayModel === 'tab'">
            <div class="tabs">
              <a-tabs
                v-model="activeKey"
                @change="activeKeyChange"
              >
                <a-tab-pane
                  forceRender
                  v-for="tab in pageData.groups"
                  :key="tab.groupCode"
                  :tab="$srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName)"
                  :class="[`${tab.groupCode}Tab`]"
                >
                  <span slot="tab">
                    <a-badge :dot="tab.showDadgeDot">
                      {{ $srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName) }}
                    </a-badge>
                  </span>
                  <slot
                    :name="`${tab.groupCode}Tab`"
                    :pageData="pageData"
                    :resultData="resultData"
                  >
                    <div
                      v-if="tab.type && tab.type === 'grid'"
                      :class="['detail-grid-box', tab.split ? 'table' : '', isDynamics(tab) ? 'lineWrap' : '']"
                    >
                      <vxe-grid
                        v-bind="getGridConfig(tab)"
                        :ref="tab.custom.ref"
                        @checkbox-all="checkboxAll"
                        @checkbox-change="checkboxChange"
                        :show-footer="showGridFooter"
                        :footer-method="
                          ({ columns, data }) => {
                            return footerMethod({ group: tab, columns, data })
                          }
                        "
                        :columns="resetCustomCols(tab.custom.columns, tab)"
                      >
                        <template #toolbar_buttons>
                          <grid-buttons
                            :tab="tab"
                            :pageData="pageData"
                            :form="form"
                            :buttons="tab.custom.buttons"
                            :url="url"
                            @checkedGridSelect="checkedGridSelect"
                            @uploadDisableFn="uploadDisableFn"
                          />
                        </template>
                        <!-- 表格富文本编辑器 -->
                        <template #rich_editor_col_render="{ row, column }">
                          <renderHtmlModal :content="row[column.property]" />
                        </template>
                        <template #renderDictLabel="{ row, column }">
                          <span>
                            {{ getDictLabel(row, column) }}
                          </span>
                        </template>
                        <!-- 货币千分位 -->
                        <template #renderCurrency="{ row, column }">
                          <span>{{ currencyFormat(row[column.property], column, tab.custom.columns) }}</span>
                        </template>
                        <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                          <a
                            v-for="(item, i) in tab.custom.optColumnList"
                            :key="'opt_' + row.id + '_' + i"
                            :title="item.title"
                            style="margin: 0 4px"
                            :disabled="item.allow ? item.allow({ ...row, refName: tab.custom.ref }) : false"
                            v-show="item.showCondition ? item.showCondition({ ...row, refName: tab.custom.ref }) : true"
                            @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                            >{{ item.title }}</a
                          >
                        </template>

                        <template #empty>
                          <m-empty
                            :displayModel="displayModel"
                            :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                          />
                        </template>
                      </vxe-grid>
                    </div>
                    <detail-form-layout
                      v-else
                      :currentEditRow="currentEditRow"
                      :tab="tab"
                      :form="form"
                      :busAccount="busAccount"
                      @getNewRouter="getNewRouter"
                      @previewPicture="previewPicture"
                    />
                  </slot>
                  <slot :name="tab.groupCode"></slot>
                </a-tab-pane>
              </a-tabs>
            </div>
          </template>
          <!-- 折叠 -->
          <template v-else-if="displayModel === 'collapse'">
            <a-collapse v-model="activeKey">
              <a-collapse-panel
                forceRender
                v-for="tab in pageData.groups"
                :key="tab.groupCode"
                :header="$srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName)"
                :class="[`${tab.groupCode}Tab`]"
              >
                <slot
                  :name="`${tab.groupCode}Tab`"
                  :pageData="pageData"
                  :resultData="resultData"
                >
                  <div
                    v-if="tab.type && tab.type === 'grid'"
                    class="detail-grid-box table"
                    :class="[isDynamics(tab) ? 'lineWrap' : '']"
                  >
                    <vxe-grid
                      :ref="tab.custom.ref"
                      v-bind="getGridConfig(tab)"
                      :columns="resetCustomCols(tab.custom.columns, tab)"
                      :show-footer="showGridFooter"
                      :footer-method="
                        ({ columns, data }) => {
                          return footerMethod({ group: tab, columns, data })
                        }
                      "
                    >
                      <template #toolbar_buttons>
                        <grid-buttons
                          :tab="tab"
                          :buttons="tab.custom.buttons"
                          @checkedGridSelect="checkedGridSelect"
                          :pageData="pageData"
                          :form="form"
                          @uploadDisableFn="uploadDisableFn"
                        />
                      </template>
                      <!-- 表格富文本编辑器 -->
                      <template #rich_editor_col_render="{ row, column }">
                        <renderHtmlModal :content="row[column.property]" />
                      </template>
                      <template #renderDictLabel="{ row, column }">
                        <span>
                          {{ getDictLabel(row, column) }}
                        </span>
                      </template>
                      <!-- 货币千分位 -->
                      <template #renderCurrency="{ row, column }">
                        <span>{{ currencyFormat(row[column.property], column, tab.custom.columns) }}</span>
                      </template>
                      <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                        <a
                          v-for="(item, i) in tab.custom.optColumnList"
                          :key="'opt_' + row.id + '_' + i"
                          :title="item.title"
                          style="margin: 0 4px"
                          :disabled="item.allow ? item.allow({ ...row, refName: tab.custom.ref }) : false"
                          v-show="item.showCondition ? item.showCondition({ ...row, refName: tab.custom.ref }) : true"
                          @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                          >{{ item.title }}</a
                        >
                      </template>

                      <template #empty>
                        <m-empty
                          :displayModel="displayModel"
                          :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                        />
                      </template>
                    </vxe-grid>
                  </div>
                  <detail-form-layout
                    v-else
                    :currentEditRow="currentEditRow"
                    :tab="tab"
                    :form="form"
                    :busAccount="busAccount"
                    @getNewRouter="getNewRouter"
                    @previewPicture="previewPicture"
                  />
                </slot>
              </a-collapse-panel>
            </a-collapse>
          </template>
          <!-- 展开折叠 -->
          <template v-else-if="displayModel === 'unCollapse'">
            <div
              forceRender
              v-for="tab in pageData.groups"
              :key="tab.groupCode"
              :tab="$srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName)"
              :class="[`${tab.groupCode}Tab`]"
            >
              <div class="item-box-title dark">
                {{ $srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName) }}
              </div>
              <slot
                :name="`${tab.groupCode}Tab`"
                :pageData="pageData"
                :resultData="resultData"
              >
                <div
                  v-if="tab.type && tab.type === 'grid'"
                  class="detail-grid-box table"
                  :class="[isDynamics(tab) ? 'lineWrap' : '']"
                >
                  <vxe-grid
                    :ref="tab.custom.ref"
                    v-bind="getGridConfig(tab)"
                    :columns="resetCustomCols(tab.custom.columns, tab)"
                    :show-footer="showGridFooter"
                    :footer-method="
                      ({ columns, data }) => {
                        return footerMethod({ group: tab, columns, data })
                      }
                    "
                  >
                    <template #toolbar_buttons>
                      <grid-buttons
                        :tab="tab"
                        :buttons="tab.custom.buttons"
                        @checkedGridSelect="checkedGridSelect"
                        :pageData="pageData"
                        :form="form"
                        @uploadDisableFn="uploadDisableFn"
                      />
                    </template>
                    <!-- 表格富文本编辑器 -->
                    <template #rich_editor_col_render="{ row, column }">
                      <renderHtmlModal :content="row[column.property]" />
                    </template>
                    <template #renderDictLabel="{ row, column }">
                      <span>
                        {{ getDictLabel(row, column) }}
                      </span>
                    </template>
                    <!-- 货币千分位 -->
                    <template #renderCurrency="{ row, column }">
                      <span>{{ currencyFormat(row[column.property], column, tab.custom.columns) }}</span>
                    </template>
                    <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                      <a
                        v-for="(item, i) in tab.custom.optColumnList"
                        :key="'opt_' + row.id + '_' + i"
                        :title="item.title"
                        style="margin: 0 4px"
                        :disabled="item.allow ? item.allow({ ...row, refName: tab.custom.ref }) : false"
                        v-show="item.showCondition ? item.showCondition({ ...row, refName: tab.custom.ref }) : true"
                        @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                        >{{ item.title }}</a
                      >
                    </template>

                    <template #empty>
                      <m-empty
                        :displayModel="displayModel"
                        :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                      />
                    </template>
                  </vxe-grid>
                </div>
                <detail-form-layout
                  v-else
                  :currentEditRow="currentEditRow"
                  :tab="tab"
                  :form="form"
                  :busAccount="busAccount"
                  @getNewRouter="getNewRouter"
                  @previewPicture="previewPicture"
                />
              </slot>
            </div>
          </template>
          <!-- 主从 -->
          <template v-else-if="displayModel === 'masterSlave'">
            <!-- {{pageData.groups}} -->
            <div>
              <a-collapse :activeKey="masterHeadActiveKey">
                <a-collapse-panel
                  forceRender
                  v-for="tab in masterHeaderData"
                  :key="tab.groupCode"
                  :header="$srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName)"
                  :class="[`${tab.groupCode}Tab`]"
                >
                  <slot
                    :name="`${tab.groupCode}Tab`"
                    :pageData="pageData"
                    :resultData="resultData"
                  >
                    <detail-form-layout
                      :currentEditRow="currentEditRow"
                      :tab="tab"
                      :form="form"
                      :busAccount="busAccount"
                      @getNewRouter="getNewRouter"
                      @previewPicture="previewPicture"
                    />
                  </slot>
                </a-collapse-panel>
              </a-collapse>
              <!-- 表行 -->
              <!-- {{pageData.groups}} -->
              <a-tabs>
                <a-tab-pane
                  forceRender
                  v-for="tab in masterGridData"
                  :key="tab.groupCode"
                  :tab="$srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName)"
                  :class="[`${tab.groupCode}Tab`, tab.split ? 'tabPaneWrap' : '']"
                >
                  <span slot="tab">
                    <a-badge :dot="tab.showDadgeDot">
                      {{ $srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName) }}
                    </a-badge>
                  </span>
                  <slot
                    :name="`${tab.groupCode}Tab`"
                    :pageData="pageData"
                    :resultData="resultData"
                  >
                    <div
                      v-if="tab.type && tab.type === 'grid'"
                      :class="['detail-grid-box', tab.split ? 'table' : '', isDynamics(tab) ? 'lineWrap' : '']"
                    >
                      <vxe-grid
                        :ref="tab.custom.ref"
                        v-bind="getGridConfig(tab)"
                        @checkbox-all="checkboxAll"
                        @checkbox-change="checkboxChange"
                        :columns="resetCustomCols(tab.custom.columns, tab)"
                        :show-footer="showGridFooter"
                        :footer-method="
                          ({ columns, data }) => {
                            return footerMethod({ group: tab, columns, data })
                          }
                        "
                      >
                        <template #toolbar_buttons>
                          <grid-buttons
                            :tab="tab"
                            :buttons="tab.custom.buttons"
                            @checkedGridSelect="checkedGridSelect"
                            :pageData="pageData"
                            :form="form"
                            @uploadDisableFn="uploadDisableFn"
                          />
                        </template>
                        <!-- 表格富文本编辑器 -->
                        <template #rich_editor_col_render="{ row, column }">
                          <renderHtmlModal :content="row[column.property]" />
                        </template>
                        <template #renderDictLabel="{ row, column }">
                          <span>
                            {{ getDictLabel(row, column) }}
                          </span>
                        </template>
                        <!-- 货币千分位 -->
                        <template #renderCurrency="{ row, column }">
                          <span>{{ currencyFormat(row[column.property], column, tab.custom.columns) }}</span>
                        </template>
                        <template #grid_opration="{ row, column, $rowIndex, $columnIndex }">
                          <a
                            v-for="(item, i) in tab.custom.optColumnList"
                            :key="'opt_' + row.id + '_' + i"
                            :title="item.title"
                            style="margin: 0 4px"
                            :disabled="item.allow ? item.allow({ ...row, refName: tab.custom.ref }) : false"
                            v-show="item.showCondition ? item.showCondition({ ...row, refName: tab.custom.ref }) : true"
                            @click="item.clickFn(row, column, $rowIndex, $columnIndex)"
                            >{{ item.title }}</a
                          >
                        </template>

                        <template #empty>
                          <m-empty
                            :displayModel="displayModel"
                            :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')"
                          />
                        </template>
                      </vxe-grid>
                    </div>
                  </slot>
                  <slot :name="tab.groupCode"></slot>
                </a-tab-pane>
              </a-tabs>
            </div>
          </template>
        </div>

        <div class="content">
          <slot name="customDetailContent"></slot>
        </div>
      </div>
    </a-spin>
    <!-- 图片预览弹窗 -->
    <a-modal
      v-drag
      :visible="previewVisible"
      :footer="null"
      @cancel="handleCancel"
    >
      <img
        alt="example"
        style="width: 100%"
        :src="previewImage"
      />
    </a-modal>
    <GridFoldDrawer
      ref="GridFoldDrawer"
      :itemColumns="itemColumns"
      :busAccount="busAccount"
      pageStatus="detail"
    />
  </div>
</template>

<script>
import { PURCHASEATTACHMENTDOWNLOADAPI, GOING_LIST_PATH, DONE_LIST_PATH, APPROVAL_PATH_LIST } from '@/utils/const'
import { getAction, postAction } from '@/api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import { bindDefaultValue, srmI18n, getLangAccount, getObjType } from '@/utils/util'
import MUpload from '@comp/mUpload'
import CustomUpload from '@comp/template/CustomUpload'
// import Breadcrumb from '@/components/tools/Breadcrumb.vue'
import { mapGetters } from 'vuex'
import GridButtons from './modules/GridButtons'
import DetailFormLayout from './modules/DetailFormLayout'
import GridFoldDrawer from '@comp/template/gridFoldDrawer'
import { EditConfig } from '@/plugins/table/gridConfig'
import { currency } from '@/filters'

import { cloneDeep } from 'lodash'

export default {
  name: 'DetailLayout',
  inject: ['tplRootRef'],
  components: {
    Popover: () => import('@comp/Popover/index.vue'),
    taskBtn,
    MUpload,
    CustomUpload,
    DetailFormLayout,
    GridButtons,
    GridFoldDrawer
    // Breadcrumb
  },
  props: {
    currentEditRow: {
      type: Object,
      default: () => {
        return {}
      }
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: srmI18n(`${getLangAccount()}#i18n_title_details`, '详情')
    },
    pageData: {
      type: Object,
      default: () => {}
    },
    isAudit: {
      type: Boolean,
      default: false
    },
    url: {
      type: Object,
      default: () => {}
    },
    // 模式布局方式： tab格式：tab，折叠格式：collapse，非折叠格式：unCollapse，单一主从格式：masterSlave
    modelLayout: {
      type: String,
      default: 'masterSlave'
    },
    // 是否使用传入布局模式
    useLocalModelLayout: {
      type: Boolean,
      default: false
    },
    reloadData: {
      type: Function
    },
    // 旧模板业务模板配置数据
    // 原模板默认不传
    pageConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    extraDetailConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    // 单个group 配置, 覆盖原配置，如高度等
    singleGroupCoverConfig: {
      type: Function,
      default: null
    },
    // 是否开启表尾功能
    showGridFooter: {
      type: Boolean,
      default: false
    },
    gridFooterMethod: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      GOING_LIST_PATH,
      DONE_LIST_PATH,
      APPROVAL_PATH_LIST,
      resultData: {},
      previewImage: '',
      previewVisible: false,
      activeKey: '',
      confirmLoading: false,
      text: 'text',
      form: {},
      tokenHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
      accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
      accept2: '.png, .jpg, .jpeg, .gif',
      elsStatusLog: {
        currentStatusIndex: 0,
        list: []
      },
      elsStatusLogList: {
        currentStatusIndex: '',
        list: []
      }
    }
  },
  computed: {
    ...mapGetters(['taskInfo']),
    imageUrl() {
      const { protocol = '', hostname = '' } = window.location || {}
      return `${protocol}//${hostname}/opt/upFiles`
      // return 'https://v5-micro-cs.oss-cn-guangzhou.aliyuncs.com'
    },
    // 旧模板默认不传业务模板配置，所以 this.$parent中获取
    examineLayout() {
      let model = ''
      if (this.pageConfig && this.pageConfig.examineLayout) {
        model = this.pageConfig.examineLayout
      } else if (this.$parent.pageConfig && this.$parent.pageConfig.examineLayout) {
        model = this.$parent.pageConfig.examineLayout
      }
      return model
    },
    displayModel() {
      let model = this.examineLayout || 'tab'
      if (this.useLocalModelLayout) {
        model = this.modelLayout
      }
      return model
    },
    panels() {
      let panels = this.pageData.groups.filter((group) => {
        return group.type != 'grid'
      })
      return panels
    },
    tabs() {
      let tabs = this.pageData.groups.filter((group) => {
        return group.type == 'grid'
      })
      return tabs
    },
    busAccount() {
      let account = this.$ls.get(USER_ELS_ACCOUNT)
      if (this.currentEditRow && this.currentEditRow.busAccount) {
        account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount || this.$ls.get(USER_ELS_ACCOUNT)
      }
      return account
    },
    masterGridData() {
      return this.pageData?.groups.filter((rs) => rs.type == 'grid')
    },
    masterHeaderData() {
      return this.pageData?.groups.filter((rs) => rs.groupType == 'head')
    },
    masterHeadActiveKey() {
      return this.pageData?.groups.find((rs) => rs.groupType == 'head')?.groupCode // 主从模式，默认一个头分组为主并展开
    },
    currentPageName() {
      // 当前页面的名称
      if (this.currentEditRow && this.currentEditRow.bizType_dictText) {
        // 待办审批进详情标题
        return this.currentEditRow.bizType_dictText
      }
      let currentPageName = this.$route.meta && this.$route.meta.titleI18nKey ? this.$srmI18n(`${this.$getLangAccount()}#${this.$route.meta.titleI18nKey}`, this.$route.meta.title) : ''
      return currentPageName + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_details`, '详情')
    },
    itemColumns() {
      const groups = this.pageData.groups || []
      let itemInfo = groups.filter((item) => item.groupCode === 'itemInfo')
      if (itemInfo.length) {
        return itemInfo[0].custom.columns
      }
      return []
    },
    itemInfo() {
      let itemInfo = []
      const groups = this.pageData.groups || []
      const group = groups.find((n) => n.groupCode === 'itemInfo')
      if (group) {
        const refName = group.custom.ref
        itemInfo = this.$refs[refName][0].getTableData().fullData || []
      }
      return itemInfo
    }
  },
  methods: {
    // 激活导航栏 父组件调用子组件
    changeActiveKey(activeKey) {
      this.activeKey = activeKey
    },
    // 从外部初始化baseForm的值
    changeForm(row) {
      this.form = row
    },

    //tab切换
    activeKeyChange(val) {
      this.$emit('activeKeyChange', val)
    },
    // 添加必填列附加样式
    // 添加排序
    resetCustomCols(cols, tab) {
      let needSortKeyList = []
      if (tab.groupName == '采购申请行信息' && tab.custom.ref == 'purchaseRequestItemList') {
        needSortKeyList = ['物料编码', '物料名称', '物料规格', '需求数量', '主单位', '辅数量', '辅单位']
      }
      cols.forEach((col) => {
        if (col.required && col.required === '1') {
          col.className = 'required-col'
          col.headerClassName = 'required-col'
        }
        if (needSortKeyList.includes(col.title)) {
          col.sortable = true
        }
      })
      return cols
    },
    footerMethod({ group, columns, data }) {
      let footerData = []
      if (columns) {
        footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '合计'
            }
            return null
          })
        ]
      }
      if (this.gridFooterMethod && getObjType(this.gridFooterMethod) === 'function') {
        return this.gridFooterMethod({ group, columns, data })
      } else {
        return footerData
      }
    },
    isDynamics(group) {
      let { groupName, groupCode, needDynamicsHeight } = group
      if (needDynamicsHeight) return true;
      if ((!!groupName && (groupName.includes('行信息') || groupName.includes('品牌信息') || groupName.includes('计量单位信息'))) || groupCode === 'saleDeliverySubList' || groupCode === 'fileInfo') return true
      return false
    },
    getGridConfig(group) {
      let assignConfig = Object.assign({}, EditConfig, this.extraEditConfig, this.extraDetailConfig)
      let { toolbarConfig, ...config } = assignConfig || {}

      // 计算当前页面最大高度
      const clientHeight = document.documentElement.clientHeight
      let contextHeight = clientHeight - 232
      let oneThird = contextHeight / 3
      let MIN = 334
      if (this.isDynamics(group)) oneThird = contextHeight
      if (contextHeight < MIN) contextHeight = MIN
      if (oneThird < MIN) oneThird = MIN

      let height
      if (this.displayModel === 'tab') {
        height = contextHeight
      } else if (this.displayModel === 'collapse') {
        height = oneThird
      } else if (this.displayModel === 'unCollapse') {
        height = oneThird
      } else if (this.displayModel === 'masterSlave') {
        if (oneThird < 442) oneThird = 442
        height = oneThird
      }
      config.height = height
      if (this.isDynamics(group)) {
        config.minHeight = MIN
        config.maxHeight = config.height
        config.height = '100%'
      }

      // 当前表行是否已配置按钮组
      if (group.custom.buttons && group.custom.buttons.length) {
        config.toolbarConfig = toolbarConfig
      }

      // 可根据分组返回自定义配置
      if (this.singleGroupCoverConfig && getObjType(this.singleGroupCoverConfig) === 'function') {
        return this.singleGroupCoverConfig(config, group, this.displayModel)
      } else {
        return config
      }
    },
    // 获取非权限码按钮组
    getAuthCodeBtns(btns) {
      let authBtns = []
      if (btns && btns.length) {
        btns.forEach((item) => {
          // 配置authorityCode做权限控制
          if (item && item.authorityCode) {
            // 有权限
            if (this.$hasOptAuth(item.authorityCode)) {
              authBtns.push(item)
            }
          } else {
            // 不配置authorityCode就不做权限控制
            authBtns.push(item)
          }
        })
      }
      return authBtns
    },
    // 货币千分位
    currencyFormat(value, info, columns = []) {
      if (!value) {
        return ''
      }
      let extend = {}
      for (let item of columns) {
        if (info.property === item.field) {
          extend = item.extend || {}
          break
        }
      }

      console.log('extend :>> ', extend)

      let symbol = (extend && extend.symbol) || ''
      let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
      return currency(value, symbol, decimals)
    },
    // 通过value显示label
    getDictLabel(row, column) {
      // 如果没有配置数据字典则走返回的field key值
      return row[column.property + '_dictText'] || row[column.property]
    },
    checkboxAll() {
      this.$emit('checkboxAll')
    },
    checkboxChange({ row }) {
      this.$emit('checkboxChange', row)
    },
    //附件上传
    handleUploadChange(info, btn, refName) {
      btn.callBack && btn.callBack(info, refName)
    },
    // 检查表格是否有被选中, cb返回promise,true打开上传，false不打开上传，如果没有cb,直接打开上传
    /**
     * cb(selectData) {
     *     return new Promise((resolve, reject)=> {
     *     // 自定义验证的规则
     *     let flag = true
     *     if (flag) {
     *          resolve(true)
     *      } else {
     *          resolve(false)
     *      }
     *     })
     * }
     */
    checkedGridSelect(btn, refName, cb) {
      let selectData = null
      let that = this
      if (this.$refs[refName]) {
        if (this.$refs[refName][0]) {
          selectData = this.$refs[refName][0].getCheckboxRecords() || this.$refs[refName][0].getRadioRecord()
        }
      }
      if (selectData && selectData.length) {
        if (cb && typeof cb === 'function') {
          if (btn.key !== 'batchDownload') {
            cb(selectData, that).then((res) => {
              if (res) {
                btn.modalVisible = true
              }
            })
          } else {
            cb(selectData, that)
          }
        } else {
          this.modalVisible = true
        }
      } else {
        console.log(btn)
        if (btn.msgType === 'batchDownload') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))
        } else {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))
        }
      }
    },
    uploadDisableFn(btn) {
      if (typeof btn.disabled === 'function') {
        return btn.disabled(this, this.form, this.pageData, btn)
      }
      return btn.disabled || false
    },
    handleCancel() {
      this.previewVisible = false
    },
    // 图片预览
    previewPicture(data) {
      if (data) {
        // this.previewImage = this.imageUrl + data
        this.previewImage = data
        this.previewVisible = true
      }
    },
    // 阶梯报价json数据组装
    initRowLadderJson(jsonData) {
      let arr = []
      if (jsonData) {
        arr = JSON.parse(jsonData)
      }
      return arr
    },
    // 阶梯报价默认显示
    defaultRowLadderJson(jsonData) {
      let arrString = ''
      if (jsonData) {
        let arr = JSON.parse(jsonData)
        arr.forEach((item, index) => {
          let ladderQuantity = item.ladderQuantity
          let price = item.price
          let netPrice = item.netPrice
          let str = `${ladderQuantity} ${price} ${netPrice} `
          let separator = index === arr.length - 1 ? '' : ','
          arrString += str + separator
        })
      }
      return arrString
    },
    // 文件下载
    handleDownload({ id, fileName }, url = '') {
      const params = {
        id
      }
      let downloadUrl = url || PURCHASEATTACHMENTDOWNLOADAPI
      if (this.url.download) {
        downloadUrl = this.url.download
      }
      getAction(downloadUrl, params, {
        responseType: 'blob'
      }).then((res) => {
        console.log(res)
        let url = window.URL.createObjectURL(new Blob([res]))
        let link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', fileName)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link) //下载完成移除元素
        window.URL.revokeObjectURL(url) //释放掉blob对象
      })
    },
    // 批量下载
    // batchDownload (rowList){
    //     if(rowList && rowList.length){
    //         rowList.map(row=>{
    //             this.handleDownload(row)
    //         })
    //     }
    // },
    // 处理头部状态流程节点
    dealStatusLog(data) {
      if (data && data.elsStatusLogList && data.elsStatusLogList.length) {
        data.elsStatusLogList.forEach((log, index) => {
          if (log.businessStatus === data.reconciliationStatus) {
            this.elsStatusLogList.currentStatusIndex = index
          }
        })
        this.elsStatusLog.list = data.elsStatusLogList
      }
    },
    queryDetail(id, cb) {
      this.confirmLoading = true
      getAction(this.url.detail, { id: id })
        .then(async (res) => {
          if (res.success) {
            try {
              if (!!this.$parent && !!this.$parent.formatPageData) {
                res.result = this.$parent.formatPageData(res.result)
              }
            } catch (error) {}

            for (var i = 0; i < this.pageData.groups.length; i++) {
              let groupItem = this.pageData.groups[i]
              try {
                if (!!this.$parent && !!this.$parent.formatTableData && groupItem?.custom?.ref) {
                  let refKey = groupItem.custom.ref
                  res.result[refKey] = await this.$parent.formatTableData(res.result[refKey] || [])
                  console.log(refKey)
                }
              } catch (error) {}
            }
            console.log('格式化完成', cloneDeep(res))
            if (this.reloadData && typeof this.reloadData === 'function') res = this.reloadData(res)
            this.$emit('loadSuccess', { res: res })
            this.resultData = cloneDeep(res.result) || {}
            this.form = res.result
            this.dealStatusLog(res.result)
            this.pageData.groups.forEach((group) => {
              if (group.type == 'grid') {
                let ref = group.custom.ref
                if (this.$refs[ref] && this.$refs[ref][0]) {
                  this.$refs[ref][0].loadData(res.result[ref])
                }
                if (group.custom.expandColumnsMethod) {
                  let expandColumns = group.custom.expandColumnsMethod()
                  group.custom.columns = group.custom.columns.concat(expandColumns)
                  delete group.custom.expandColumnsMethod
                }

                if (Object.keys(group).includes('showDadgeDot')) {
                  //判断tab是否需要加红点
                  group.showDadgeDot = res.result[ref].length > 0
                }
              } else if (!group.type || !group.type === 'grid') {
                this.$nextTick(() => {
                  const formFields = group.custom.formFields || []
                  formFields.forEach((item) => {
                    const { bindFunction, fieldName, fieldType, groupCode, defaultValue } = item

                    if (bindFunction && typeof bindFunction === 'function') {
                      const parentRef = this.$refs[groupCode]
                      const groupData = this.pageData.groups[this.currentStep]
                      const value = this.form[fieldName] || bindDefaultValue(defaultValue)
                      if (fieldType !== 'selectModal') {
                        if (fieldType === 'input') {
                          bindFunction.call(null, parentRef, this.pageData, groupData, value, item, this.form)
                        } else {
                          bindFunction.call(null, parentRef, this.pageData, groupData, value, [], '', this.form, '', this)
                        }
                      }
                    }
                  })
                })
              }
            })
            if (this.pageData && this.pageData.groups.length) {
              this.activeKey = this.pageData.groups[0].groupCode
            }
          } else {
            this.$message.warning(res.message)
          }
          cb && cb(res.result, this.pageData)
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getPageData() {
      const that = this
      let params = { ...this.form }
      this.pageData.groups.forEach((group) => {
        if (group.type == 'grid') {
          let ref = group.custom.ref
          params[ref] = that.$refs[ref].length && that.$refs[ref][0].getTableData().fullData
        }
      })
      return params
    },
    setPromise() {
      let that = this
      let promise = this.pageData.groups.map((group) => {
        if (group.type == 'grid') {
          return that.$refs[group.custom.ref][0].validate(true)
        } else {
          if (that.$refs[group.groupCode]) return that.$refs[group.groupCode][0].validate()
          else {
            return new Promise((resolve, reject) => {
              const requiredList = group.custom.formFields.filter((i) => i.required === '1')
              let res = true
              requiredList.forEach((i) => {
                if (!this.form[i.fieldName]) res = false
              })
              if (res) resolve(res)
              else reject(res)
            })
          }
        }
      })
      return promise
    },
    handValidate(url, params, callback) {
      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({
              status: 'success',
              res
            }),
            (err) => ({
              status: 'error',
              err
            })
          )
        )
      let promise = this.setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              this.currentStep = i
              return callback({ status: 'error' })
            }
          }
          if (flag) return callback && callback(url, params, this)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    handleSend(type = 'public', callback) {
      const handlePromise = (list = []) =>
        list.map((promise) =>
          promise.then(
            (res) => ({
              status: 'success',
              res
            }),
            (err) => ({
              status: 'error',
              err
            })
          )
        )
      let promise = this.setPromise()
      Promise.all(handlePromise(promise))
        .then((result) => {
          let flag = false
          for (let i = 0; i < result.length; i++) {
            if (result[i].status === 'success') {
              flag = true
            } else {
              this.currentStep = i
              return
            }
          }
          if (flag) this.postData(type, callback)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    postData(type, callback) {
      let params = this.getPageData()
      let url = type === 'public' ? this.url.public : this.voucherId ? this.url.edit : this.url.add
      this.confirmLoading = true
      postAction(url, params)
        .then((res) => {
          const messageType = res.success ? 'success' : 'error'
          let resMsg = res.message
          if (!!resMsg && resMsg.indexOf('\n') >= 0) {
            const h = this.$createElement
            let strList = resMsg.split('\n')
            strList = strList.map((str, strIndex) => {
              return h(strIndex === 0 ? 'span' : 'div', null, str)
            })
            resMsg = h('span', null, strList)
          }
          this.$message[messageType](resMsg)
          if (res.success && this.refresh) {
            this.queryDetail()
          }
          if (res.success && type === 'public') {
            this.$parent.goBack()
          } else {
            // 自定义回调
            return callback && callback(params, this)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    showLoading() {
      this.confirmLoading = true
    },
    hideLoading() {
      this.confirmLoading = false
    },
    // 超链接 跳转
    getNewRouter(value, item) {
      let that = this
      let linkConfig = item?.extend?.linkConfig || {}
      if (item?.extend?.handleBefore && typeof item.extend.handleBefore === 'function') {
        let callbackObj = item.extend.handleBefore(this.form, linkConfig, that, { pageConfig: this.$parent.pageConfig }) || {}
        linkConfig = { ...linkConfig, ...callbackObj }
      }
      if (value && linkConfig.actionPath && linkConfig.bindKey) {
        let query = {
          [linkConfig.primaryKey]: this.form[linkConfig.bindKey],
          ...linkConfig.otherQuery,
          t: new Date().getTime()
        }
        this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
      }
    }
  },
  mounted() {
    this.$store.dispatch('modifyBusAccountLangData', { busAccount: this.busAccount })
  }
}
</script>

<style lang="less" scoped>
@primary-color: #1890ff;
.detail-page {
  :deep(.lineWrap) {
    .vxe-table--fixed-left-wrapper,
    .vxe-table--fixed-right-wrapper {
      height: calc(100% - 8px) !important;
    }
  }
  .step-status {
    padding: 36px 0px 30px 0px;
  }
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin: 1px 0px 0 1px;
    padding: 8px 14px;
    background: #fff;
    .btnGroups {
      text-align: right;
      .ant-btn {
        & + .ant-btn {
          margin-left: 10px;
        }
      }
    }
  }
  .content {
    // margin: 0px 1px;
    padding: 8px;
    margin: 8px;
    background: #fff;
    .preview {
      cursor: pointer;
    }
  }

  :deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
    background: #f5f6f7;
  }

  :deep(.ant-descriptions-item-content) {
    width: 16.66%;
    max-width: 16.66%;
  }
  :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title),
  :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description) {
    color: @primary-color;
  }
  :deep(.rich-editor-display-box p) {
    margin-bottom: 0px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 500px;
  }
  .item-box-title {
    padding: 0 7px;
    border: 1px solid #ededed;
    height: 34px;
    line-height: 34px;
    margin: 6px 0;
    &.dark {
      background: #f2f2f2;
    }
  }
  :deep(.vxe-table--render-default.size--mini) {
    font-size: 13px;
  }
}
</style>

<style lang="scss" scoped>
.tabPaneWrap {
  display: flex;
  flex-direction: column;
  .table {
    flex: 1;
    min-height: 0;
    :deep(.vxe-toolbar) {
      height: 0;
    }
  }
}
.richEditorModel {
  :deep(p) {
    margin-bottom: 0px;
  }
}
:deep(.vxe-body--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
:deep(.vxe-header--column.required-col) {
  background-color: #f6ebe8;
  background-image: linear-gradient(#fff9f7, #fff9f7), linear-gradient(#fff9f7, #fff9f7);
}
</style>
